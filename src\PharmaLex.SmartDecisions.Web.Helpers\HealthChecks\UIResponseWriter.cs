﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using PharmaLex.Helpers;
using Newtonsoft.Json;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Helpers.HealthChecks;
public class UIResponseWriter
{
    private readonly AppSettingsHelper appSettingsHelper;

    public UIResponseWriter(AppSettingsHelper appSettingsHelper)
    {
        this.appSettingsHelper = appSettingsHelper;
    }

    public Task WriteResponse(HttpContext context, HealthReport healthReport)
    {
        context.Response.ContentType = "application/json; charset=utf-8";

        var result = JsonConvert.SerializeObject(new
        {
            status = healthReport.Status.ToString(),
            app = Assembly.GetEntryAssembly()?.GetName().Name,
            version = appSettingsHelper.BuildInfo,
            details = healthReport.Entries.Select(e => new
            {
                key = e.Key,
                description = e.Value.Description,                   
                status = e.Value.Status.ToString(),
                exception = e.Value.Exception?.Message,
                data = e.Value.Data
            })
        }, Formatting.Indented);

        return context.Response.WriteAsync(result);}
}
