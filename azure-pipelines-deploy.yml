parameters:
  - name: decisionPackageLocation
    type: string
    default: 'drop/PharmaLex.SmartDecisions.Web.zip'
  - name: newsletterPackageLocation
    type: string
    default: 'newsletter/PharmaLex.NewsletterApp.zip'

pool: 'pv-pool'

trigger: none
pr: none

resources:
  pipelines:
    - pipeline: build-pipeline
      source: SmartDecisions.Build
      project: SmartDecisions
      trigger:
        branches:
          include:
            - develop

stages:
  - template: ./azure-pipelines-template.yml
    parameters:
      decisionPackageLocation: ${{ parameters.decisionPackageLocation }}
      newsletterPackageLocation: ${{ parameters.newsletterPackageLocation }}