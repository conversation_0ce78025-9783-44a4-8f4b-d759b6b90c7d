﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <OutputType>Exe</OutputType>
    <RootNamespace>PharmaLex.NewsletterApp</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="appSettings.json" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="appSettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.13.2" />
    <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.3.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="2.0.1" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Storage" Version="6.6.1" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Timer" Version="4.3.1" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="2.0.2" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.0" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.71.1" />
    <PackageReference Include="Microsoft.Identity.Client.Extensions.Msal" Version="4.71.1" />
	<PackageReference Include="System.Drawing.Common" Version="6.0.0" />
	<PackageReference Include="System.Text.Json" Version="9.0.4" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>
  <ItemGroup>
    <RuntimeHostConfigurationOption Include="System.Globalization.AppLocalIcu" Value="********" />
    <PackageReference Include="Microsoft.ICU.ICU4C.Runtime" Version="********" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.SmartDecisions.Data\PharmaLex.SmartDecisions.Data.csproj" />
    <ProjectReference Include="..\PharmaLex.SmartDecisions.Entities\PharmaLex.SmartDecisions.Entities.csproj" />
    <ProjectReference Include="..\PharmaLex.SmartDecisions.Web.Helpers\PharmaLex.SmartDecisions.Web.Helpers.csproj" />
    <ProjectReference Include="..\PharmaLex.SmartDecisions.Web.Models\PharmaLex.SmartDecisions.Web.Models.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
  </ItemGroup>
</Project>
