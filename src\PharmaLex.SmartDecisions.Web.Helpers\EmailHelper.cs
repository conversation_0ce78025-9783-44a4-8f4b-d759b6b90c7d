﻿using System;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Helpers;

public static class EmailHelper
{
    private static readonly string[] Domains = [
        "cencora.com", "amerisourcebergen.com", "phlexglobal.com", "pharmalex.com", "yespharmaservices.onmicrosoft.com"
    ];

    public static bool IsInternalDomainUser(string? email)
    {
        if (string.IsNullOrEmpty(email))
        {
            return false;
        }

        var emailParts = email.Split("@");

        if (emailParts.Length < 2)
        {
            throw new ArgumentException("Invalid email", nameof(email));
        }

        return Domains.Contains(emailParts[1].ToLower());
    }
}
