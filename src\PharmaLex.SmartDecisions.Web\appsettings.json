{"Logging": {"LogLevel": {"Default": "Warning"}}, "AzureAdB2C": {"Instance": "https://smartphlexb2c.b2clogin.com", "ClientId": "41d5de74-2e7e-4423-85f5-45b3611159fd", "Domain": "smartphlexb2c.onmicrosoft.com", "SignedOutCallbackPath": "/signout/B2C_1_signin", "SignUpSignInPolicyId": "B2C_1_signup_signin_plx", "ResetPasswordPolicyId": "b2c_1_password_reset", "EditProfilePolicyId": "b2c_1_profile_edit", "CallbackPath": "/signin-oidc"}, "AzureAdB2CPolicy": {"Tenant": "smartphlexb2c", "TenantId": "f8b90da3-e024-492c-800e-1b793397f942", "ClientId": "41d5de74-2e7e-4423-85f5-45b3611159fd", "SigningCertThumbprint": "E1BA3541BA2553B9BC40927F1359F264E1724D61", "LinkExpiresAfterDays": 7, "Policies": [{"PolicyId": "B2C_1A_signup_invitation", "CallbackPath": "/signin-oidc-invite", "ApplicationCallbackPath": "/signup-invitation"}, {"PolicyId": "B2C_1_signup_signin_pharmalex", "CallbackPath": "/signin-oidc-plx", "ApplicationCallbackPath": "/signin"}, {"PolicyId": "B2C_1A_signin_signature", "CallbackPath": "/signin-signature-oidc", "ApplicationCallbackPath": "/signature"}]}, "AzureAdGraph": {"ClientId": "7bc93d0f-ddf4-40da-8c80-542d161538e6", "Domain": "yes-services.eu"}, "AzureAdB2CGraph": {"ClientId": "5c76d17d-764a-4e8e-bef7-c39afd6fb3bd", "Domain": "smartphlexb2c.onmicrosoft.com"}, "AppSettings": {"BuildInfo": "Build:local", "BuildNumber": "local", "Version": "5.8.0", "SystemAdminEmail": "<EMAIL>", "SmartPHLEXAdminEmail": "<EMAIL>", "CompanyUserLoginEmailTemplateId": "d-546190ec301d4d19812f6aa489b96126", "CompanyUserSignUpEmailTemplateId": "d-693bf4227879492da3482d6ff2edf4dd", "RecordExpirationNotificationTemplateId": "d-817d5c32c09642258da3c0fe39806db2", "NotificationKey": "btG74Umxdr3110oJ", "NonAdminNewsletterPublicationHourCET": "15", "NonAdminNewsletterPublicationDays": "2,5", "ImpactAssessmentBlobPath": "ImpactAssessment", "ImpactAssessmentNotIndexedBlobPath": "ImpactAssessmentNotIndexed", "HtmlArticleBlobPath": "HtmlArticle", "NewsletterRetryURL": "http://localhost:7071/api/retrydailyynewsletters", "NewsletterMonthlyRetryURL": "http://localhost:7071/api/retrymonthlynewsletters", "NewsletterRetryFunctionMasterKey": "ignored-in-dev", "NewsletterMonthlyRetryFunctionMasterKey": "ignored-in-dev", "NewsletterUrlKeyLength": "32", "NewsletterUrlKeyLengthLegacy": "8", "NewsletterArticleUrlKeySeparatorIndex": "32", "NewsletterArticleUrlKeySeparatorIndexLegacy": "8", "NewsletterArticleKeyMinLength": 10}, "Static": {"App": "smartdecisions", "Env": "dev", "Cdn": "https://phcgvcdn-endpoint.azureedge.net", "Version": "v1.31.0", "Container": "content"}, "AzureStorage": {"Account": "sdcdevsharedeun", "Container": "smartdecisions"}, "AzureSearch": {"ServiceName": "ss-nonprod-ss-eun", "IndexName": "sdc-dev-index", "MaxRecords": "1000", "IsEnabled": false, "IndexerName": "sdc-dev-indexer", "DataSourceName": "sdc-dev", "BlobFolder": "ImpactAssessment", "BlobContainer": "smartdecisions"}, "HtmlToPdfConverter": {"Url": "http://localhost-not-used"}, "ConnectionStrings": {"default": "Data Source=.;Initial Catalog=smartdecisions-dev;Integrated Security=true;MultipleActiveResultSets=True"}, "KeyVaultName": "sdc-dev-kv-eun", "VisualStudioTenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77", "SqlOptions": {"MigrationsAssembly": "PharmaLex.SmartDecisions.Data", "EnableDetailedErrors": true}, "AllowedHosts": "*", "AxonCoreAuthentication": {"Audience": "https://decisions-dev.smartphlex.com/", "Issuer": "https://app-dev.smartphlex.com", "PublicKeyApiHost": "https://app-dev.smartphlex.com/api/core", "SmartPhlexHostUrl": "https://app-dev.smartphlex.com", "Tenant": "phlexglobal", "AppCodeName": "smartdecisions"}}