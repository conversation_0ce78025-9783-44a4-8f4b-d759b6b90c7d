﻿@using Microsoft.Extensions.Configuration
@inject IConfiguration Configuration
@{
    ViewData["Title"] = "";
    var smartPhlexLoginUrl = Configuration.GetValue<string>("AxonCoreAuthentication:SmartPhlexHostUrl");
    var tenant = Configuration.GetValue<string>("AxonCoreAuthentication:Tenant");
    var appCodeName = Configuration.GetValue<string>("AxonCoreAuthentication:AppCodeName");
    var postLoginRedirect = ViewData["postLoginRedirect"] ?? "/";
    var axonLoginUrl = $"{smartPhlexLoginUrl}/{tenant}/externallogin/{appCodeName}?postLoginRedirect={postLoginRedirect}";
}
<div class="login-container">
    <div class="login-box">
        <div class="app-title">
            <div class="dual-title">
                <span class="app-title-regular">Knowledge.</span>
                <span>Accelerated.</span>
            </div>
        </div>
        <div class="login-prompt-container">
            <span class="login-prompt">
                Log in to your Cencora PharmaLex account
            </span>
        </div>
        @if (this.ViewData["unauthorised"] != null)
        {
        <p><i class="m-icon error-color">warning</i>@ls.LocaliseInterpolate("(news).(account).login-account-unauthorized-format", "SMART<strong>DECISIONS</strong>")</p>
        }
        <a id="login" href="/login" class="button">@ls.Localise("(news).(account).sign-in")</a>
        
        <div>
            <h5>Internal Cencora users ONLY</h5>
            <a id="external-login" href="@axonLoginUrl" class="button">Login with SmartPhlex</a>
        </div>
        <div id="warning" class="flex hidden">
            <span class="m-icon mr-2 warning-color">warning_amber</span>
            <div>
                <p>This system is optimised for use with modern browsers and you are currently using an older browser which is not supported.</p>
                <p>Please use an alternative browser to use this system such as the latest version of Chrome, Firefox, Edge or Safari to continue.</p>
            </div>
        </div>
        <div class="logo-container">
            <img src="@VersionCdn.GetUrl("images/Cencora_PharmaLex_logo_small.png")" alt="Cencora PharmaLex logo">
        </div>
    </div>
</div>

@section Scripts {
<script type="text/javascript">
    var plx = {
    init: function () {
    try {
    eval('var x = () => { return true; }');
    } catch (e) {
    document.getElementById('smartphlex-login').style.display = 'none';
    document.getElementById('plx-login').style.display = 'none';
    document.getElementById('warning').style.display = 'block';
    }
    }
    };
    (function () { plx.init(); })();
</script>
}
