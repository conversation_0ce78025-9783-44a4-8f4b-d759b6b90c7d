﻿using Axon.Core.Authentication.Interfaces;
using Axon.Core.Shared.Auth;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using PharmaLex.SmartDecisions.Web.Helpers;

namespace PharmaLex.SmartDecisions.Web.Authentication;

public class AuthCallback : IAuthCallback
{
    private static readonly string[] UserEmailClaims = [AxonClaimTypes.Email, AxonClaimTypes.UserAlternativeIdentities];

    public async Task OnAuthReceived(ClaimsPrincipal claimsPrincipal, HttpContext context)
    {
        var claimsIdentity = claimsPrincipal.Identity as ClaimsIdentity;

        if (claimsIdentity == null)
        {
            throw new InvalidOperationException("Invalid Claims Principal");
        }

        var userEmails = claimsIdentity.Claims.Where(c => UserEmailClaims.Contains(c.Type))
            .Select(c => c.Value.ToLower()).ToList();
        var primaryEmail = claimsIdentity.GetClaimValue(AxonClaimTypes.Email).ToLower();

        var dbContextResolver = context.RequestServices.GetService<IPlxDbContextResolver>();
        var repoFactory = new RepositoryFactory(dbContextResolver, primaryEmail);
        var userRepository = repoFactory.CreateTracking<User>();

        var user = await userRepository.Configure(x =>
                x.Include(y => y.UserClaim)
                    .ThenInclude(y => y.Claim)
            )
            .FirstOrDefaultAsync(x => userEmails.Contains(x.Email.ToLower()));

        if (user == null)
        {
            if (!claimsPrincipal.IsPharmaLexUser() && !EmailHelper.IsInternalDomainUser(primaryEmail))
            {
                context.Response.Redirect("/unauthorised");
                return;
            }

            user = new User
            {
                GivenName = claimsIdentity.GetClaimValue(ClaimConstants.GivenName),
                FamilyName = claimsIdentity.GetClaimValue(ClaimConstants.FamilyName),
                Email = primaryEmail,
                LastLoginDate = DateTime.Now
            };
            userRepository.Add(user);

            claimsIdentity.AddClaim(new System.Security.Claims.Claim("plx:companyid", "0"));
            claimsIdentity.AddClaim(new System.Security.Claims.Claim("plx:userisactive", "false"));
        }
        else
        {
            if (!claimsPrincipal.IsPharmaLexUser() && !EmailHelper.IsInternalDomainUser(primaryEmail))
            {
                var companyUserRepo = repoFactory.Create<CompanyUser>();
                var companyUser = await companyUserRepo.Configure(o => o
                    .Include(x => x.Company)
                    .ThenInclude(x => x.CompanyContentType)
                    .Include(x => x.Company)
                    .ThenInclude(x => x.CompanyNewsCategory)
                ).FirstOrDefaultAsync(x => x.UserId == user.Id && x.Active);

                if (companyUser == null || (companyUser.Company.CompanyContentType.Count < 1 && companyUser.Company.CompanyNewsCategory.Count < 1))
                {
                    context.Response.Redirect("/unauthorised");
                    return;
                }

                claimsIdentity.AddClaim(new System.Security.Claims.Claim("plx:companyid", companyUser.CompanyId.ToString()));
                claimsIdentity.AddClaim(new System.Security.Claims.Claim("plx:userisactive", companyUser.Active.ToString()));
            }
            else
            {
                claimsIdentity.AddClaim(new System.Security.Claims.Claim("plx:companyid", "0"));
                claimsIdentity.AddClaim(new System.Security.Claims.Claim("plx:userisactive", "false"));
            }

            claimsIdentity.AddClaim(new System.Security.Claims.Claim("plx:companyid", "0"));
            claimsIdentity.AddClaim(new System.Security.Claims.Claim("plx:userisactive", "false"));

            foreach (var uc in user.UserClaim)
            {
                claimsIdentity.AddClaim(new System.Security.Claims.Claim($"{uc.Claim.ClaimType}:{uc.Claim.Name}", uc.ClaimId.ToString()));
            }

            user.LastLoginDate = DateTime.Now;
        }

        await userRepository.SaveChangesAsync();

        var distributedCacheService = context.RequestServices.GetService<IDistributedCacheService>();
        await distributedCacheService.InvalidateAsync("User", "UserClaim", "UserClaim.Claim");
        claimsIdentity.AddClaim(new System.Security.Claims.Claim("plx:userid", user.Id.ToString()));

        var cir = repoFactory.Create<ContentItem>();
        if (await cir.Configure().FirstOrDefaultAsync(x => x.Owner.ToLower() == primaryEmail) != null)
        {
            claimsIdentity.AddClaim(new System.Security.Claims.Claim("plx:RecordOwner", "true"));
        }
    }
}
