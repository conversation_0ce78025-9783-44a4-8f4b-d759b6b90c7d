/*Units*/
/*Colours*/
/*Fonts*/
/*Space*/
/*This will apply the quality color to the brand variable, globally (within the context of this application).*/
.m-icon-group i.confirm-color {
  color: #007F50; }

.m-icon-group i.info-color, .m-icon-group i.number-color {
  color: #0073BE; }

.m-icon-group i.failed-color {
  color: #E22F00; }

/* Badges */
.badges {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }
  .badges .badge {
    border-radius: 5px;
    background-color: #3B3B3B;
    color: #fff;
    font-size: 12px;
    text-align: center;
    margin-right: .5rem;
    padding: 5px 8px;
    font-weight: 400; }
    .badges .badge.inactive {
      background-color: #3B3B3B;
      color: #CACACA; }
    .badges .badge.medium {
      font-size: .85rem; }
    .badges .badge.large {
      font-size: 1rem;
      padding: 8px 12px; }

/*Height/Width*/
.width-0 {
  width: 0% !important; }

.width-10 {
  width: 10% !important; }

.width-20 {
  width: 20% !important; }

.width-30 {
  width: 30% !important; }

.width-40 {
  width: 40% !important; }

.width-50 {
  width: 50% !important; }

.width-60 {
  width: 60% !important; }

.width-70 {
  width: 70% !important; }

.width-80 {
  width: 80% !important; }

.width-90 {
  width: 90% !important; }

.width-100 {
  width: 100% !important; }

.height-0 {
  height: 0% !important; }

.height-10 {
  height: 10% !important; }

.height-20 {
  height: 20% !important; }

.height-30 {
  height: 30% !important; }

.height-40 {
  height: 40% !important; }

.height-50 {
  height: 50% !important; }

.height-60 {
  height: 60% !important; }

.height-70 {
  height: 70% !important; }

.height-80 {
  height: 80% !important; }

.height-90 {
  height: 90% !important; }

.height-100 {
  height: 100% !important; }

.sticky {
  z-index: 80; }
  .sticky.top {
    top: -1rem; }
  .sticky.bottom {
    bottom: -1rem; }

/*Units*/
/*Colours*/
/*Fonts*/
/*Space*/
table {
  border-collapse: separate; }
  table thead {
    background: white; }
  table th, table td {
    font-size: .75rem; }
  table tbody tr:nth-child(odd) {
    background: #F5F5F5; }
  table.bordered {
    border: 1px solid #8A8A8A; }
    table.bordered th, table.bordered td {
      border-collapse: collapse;
      border: 1px solid #8A8A8A; }
  table th.actions > div i.m-icon.clear.active {
    font-weight: inherit; }

.table-header .sorter:before, .table-header .sorter.sorting_asc:before {
  -webkit-transform: none;
          transform: none; }

@media screen and (max-width: 1780px) {
  html, body {
    font-size: 90%; } }

@media screen and (max-width: 1500px) {
  html, body {
    font-size: 82%; } }

@media screen and (max-width: 1350px) {
  html, body {
    font-size: 74%; } }

.core-container {
  min-width: 80vw !important;
  max-width: 100vw !important; }

/*Drag/Drop*/
.draggable-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  background-color: #F5F5F5;
  color: #fff;
  padding: .75rem;
  border-radius: 0.75rem;
  font-size: .85rem;
  line-height: 1.25rem;
  margin-bottom: 1rem;
  /*Default */
  /*Medicines*/
  /*Biocides */
  /*Cosmetics */
  /*Nutrition */
  /*Medical devices */ }
  .draggable-group:first-of-type {
    margin-top: 1.5rem; }
  .draggable-group.category-default {
    background-color: #461E96; }
  .draggable-group.category-medicines {
    background-color: #095F3B; }
  .draggable-group.category-biocides {
    background-color: #119D63 !important; }
  .draggable-group.category-cosmetics {
    background-color: #FCA2C9; }
  .draggable-group.category-nutrition {
    background-color: #DD7005; }
  .draggable-group.category-med-devices {
    background-color: #750748; }
  .draggable-group h5 {
    margin-bottom: 0; }
  .draggable-group.target-entered {
    background: #CACACA !important;
    color: #1E1E1E;
    outline: 2px #1E1E1E dotted;
    -webkit-transition: all .3s;
    transition: all .3s; }

.drag-drop-list {
  position: relative;
  /*Medicine*/
  /*Biocides*/
  /*Cosmetics*/
  /*Nutrition*/
  /*Devices*/ }
  .drag-drop-list .category-medicines {
    background-color: #E7F7FF;
    color: #1E1E1E; }
  .drag-drop-list .category-biocides {
    background-color: #E7F7FF;
    color: #1E1E1E; }
  .drag-drop-list .category-cosmetics {
    background-color: #FFF0F6;
    color: #1E1E1E; }
  .drag-drop-list .category-nutrition {
    background-color: #FCD9D1;
    color: #1E1E1E; }
  .drag-drop-list .category-med-devices {
    background-color: #FFF0F6;
    color: #1E1E1E; }

.import-summary {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #CACACA;
  margin-bottom: 1rem; }
  .import-summary i {
    font-size: 2rem;
    color: #FBC618; }
  .import-summary p {
    margin: 0;
    padding-bottom: 0;
    font-size: 1rem;
    padding-left: 1rem; }

.import-errors ul li {
  position: relative;
  padding-left: 1.5rem;
  font-size: .85rem; }
  .import-errors ul li:before {
    position: absolute;
    top: 0;
    left: 0;
    content: '\e002';
    font-family: 'MaterialIcons';
    color: #FBC618;
    font-size: 1rem; }

/*Topics*/
.topic-column {
  padding: 0 1.5rem; }
  .topic-column:first-of-type {
    padding-left: 0; }
  .topic-column:last-of-type {
    padding-right: 0; }

.topic-sidebar {
  border-left: 1px solid #F5F5F5; }

.topic-value-group {
  margin-bottom: 2rem; }
  .topic-value-group ul {
    list-style-type: disc; }
    .topic-value-group ul li {
      page-break-inside: avoid;
      -webkit-column-break-inside: avoid;
         -moz-column-break-inside: avoid;
              break-inside: avoid;
      margin: .5rem 0 .75rem 1rem;
      padding-left: 0;
      list-style-type: disc;
      font-size: .85rem;
      line-height: 1.25rem; }
      .topic-value-group ul li strong {
        display: block; }

/*Jim 16 nov*/
.search-option {
  display: grid;
  grid-template-columns: calc(10% - 2rem) calc(90% - 2rem);
  grid-gap: 0 2rem;
  gap: 0 2rem; }

.dragdropspacer {
  width: 100%;
  height: 5px;
  background-color: transparent; }

.dragdropspacermeds {
  height: 15px; }

.dragdropspacerhover {
  background-color: #F5F5F5;
  cursor: move;
  margin: .50rem 0 .50rem 0;
  padding: .25rem;
  width: 100%;
  height: 10px;
  border-radius: 0.75rem; }

.draggable-group {
  margin-bottom: 0.5rem; }

.vertical-line {
  margin: 0 1rem;
  height: 100%;
  border-left: 1px solid #CACACA; }

.group-section-container {
  min-height: 30rem; }

.chip {
  background-color: #E8E8E8;
  border-radius: 0.8rem;
  color: #000;
  display: inline-block;
  padding: 0.5rem;
  margin: 0.2rem;
  font-size: 0.85rem; }

.hero-banner-tooltip {
  position: relative; }
  .hero-banner-tooltip .hero-banner-tooltiptext {
    visibility: hidden;
    background-color: #3B3B3B;
    color: #fff;
    text-align: center;
    border-radius: 0.5rem;
    padding: 0.5rem;
    -webkit-transition: all 0.4s;
    transition: all 0.4s;
    opacity: 0;
    font-size: 0.85rem;
    position: absolute;
    top: -3.7rem;
    left: 0;
    width: 60rem;
    z-index: 20;
    font-weight: 200; }
  .hero-banner-tooltip:hover .hero-banner-tooltiptext {
    visibility: visible;
    opacity: 1; }

.hero-banner-textarea {
  width: 100%;
  resize: vertical;
  max-height: 15rem; }

.loader {
  position: absolute;
  height: 100%;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: start;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  opacity: .8;
  z-index: 10000;
  background-color: #fff; }

.add-view-display {
  width: 23rem !important; }

.view-topic-dialog .dialog-content {
  padding-top: 0px;
  padding-bottom: 0px; }

.view-topic-dialog #dialog-closer {
  display: none; }

/* News Search Article*/
#news-wall {
  font-size: 0.85rem;
  padding: 50px; }

#news-wall a {
  color: #333333; }

#news-wall article {
  padding: 0 0 10px 0; }

#news-wall article h1 {
  font-weight: 400;
  font-size: 24px;
  line-height: 29px;
  border-top: 1px solid #E5E5E5;
  margin: 0;
  padding: 10px 0; }

#news-wall article section {
  padding: 15px 0;
  border-top: 1px solid #E5E5E5; }

#news-wall article section.meta {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

#news-wall article section.meta div {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0 30px;
  overflow: hidden;
  -webkit-box-sizing: border-box;
          box-sizing: border-box; }

#news-wall article section.meta div strong {
  padding: 0 5px 0 0; }

#news-wall article section.meta div span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; }

#news-wall article section.meta div:first-of-type {
  padding-left: 0; }

#news-wall article section.meta div:last-of-type {
  padding-right: 0; }

#news-wall article section#pills {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding-bottom: 5px; }

#news-wall article section#pills div {
  position: relative; }

#news-wall #pills span {
  display: inline-block;
  margin: 0 5px 10px 0;
  padding: 5px 12px;
  border-radius: 12px;
  color: #FFFFFF; }

#news-wall #pills .legend .legend-help:hover + .legend-container {
  visibility: visible; }

#news-wall #pills .legend .legend-container {
  visibility: hidden; }

#news-wall #pills .legend-container .legend-container-items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  border: 1px solid #E5E5E5;
  padding: 0.5rem;
  position: absolute;
  border-radius: 0.4rem;
  top: -1.8rem;
  left: -10.5rem;
  z-index: 10;
  background: white;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease; }
  #news-wall #pills .legend-container .legend-container-items div {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    font-size: 0.75rem; }
  #news-wall #pills .legend-container .legend-container-items div span {
    width: 20px;
    height: 20px;
    border-radius: 0; }

#news-wall span.purple {
  background-color: #4A1671; }

#news-wall span.gray {
  background-color: #557595; }

#news-wall span.orange {
  background-color: #DF8043; }

#news-wall span.red {
  background-color: #B10000; }

#news-wall footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 12px;
  padding: 35px 0;
  border-top: 1px solid #E5E5E5; }

#news-wall footer div {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

#news-wall footer span {
  display: block;
  width: 20px;
  height: 20px; }

#news-wall footer i {
  display: block;
  line-height: 20px;
  padding: 0 13px 0 8px;
  font-style: normal; }

@media screen and (max-width: 1200px) {
  #news-wall {
    padding: 30px; }
  #news-wall article section.meta {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    padding-bottom: 5px !important; }
  #news-wall article section.meta div {
    margin-bottom: 10px;
    padding: 0;
    width: 50%; }
  #news-wall article section.meta div:nth-child(odd) {
    padding-right: 5px; }
  #news-wall article section.meta div:nth-child(even) {
    padding-left: 5px; }
  #news-wall article section.meta div.source {
    width: 100%; } }

@media screen and (max-width: 768px) {
  #news-wall {
    font-size: 12px;
    padding: 20px; }
  #news-wall article section.meta div {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; } }

@media screen and (max-width: 480px) {
  #news-wall footer {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap; }
  #news-wall footer div {
    width: 50%;
    margin-bottom: 10px; } }

input[type=search] {
  background-color: white;
  border: none;
  border-radius: 0.5rem; }

.search-download-articles {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-line-pack: center;
      align-content: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  background-color: white;
  padding: 1rem;
  margin-bottom: -1.25rem; }
  .search-download-articles-selected {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-line-pack: center;
        align-content: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center; }

.search-icon {
  position: relative;
  font-size: 1.25rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  left: 0.25rem;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: #8A8A8A; }

.search-content {
  margin-bottom: 0.75rem;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: start;
  -ms-flex-line-pack: center;
      align-content: center;
  border-top: 1px solid #E8E8E8;
  padding-top: 1rem; }
  .search-content__input {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-line-pack: center;
        align-content: center;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: start;
    border: 1px solid #CACACA;
    border-radius: 0.55rem; }
    .search-content__input:hover {
      border-color: #8A8A8A; }
    .search-content__input:has(.search-content-input:focus), .search-content__input:has(.search-content-input:active) {
      border-color: #461E96; }
    .search-content__input input {
      outline: none;
      width: 53.5rem; }
      .search-content__input input::-webkit-search-decoration, .search-content__input input::-webkit-search-cancel-button, .search-content__input input::-webkit-search-results-button, .search-content__input input::-webkit-search-results-decoration {
        -webkit-appearance: none; }
      .search-content__input input::-webkit-input-placeholder {
        color: #8A8A8A;
        opacity: 1; }
      .search-content__input input::-moz-placeholder {
        color: #8A8A8A;
        opacity: 1; }
      .search-content__input input:-ms-input-placeholder {
        color: #8A8A8A;
        opacity: 1; }
      .search-content__input input::-ms-input-placeholder {
        color: #8A8A8A;
        opacity: 1; }
      .search-content__input input::placeholder {
        color: #8A8A8A;
        opacity: 1; }
  .search-content__only-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    margin-left: 0.5rem;
    border-radius: 0.5rem;
    -webkit-transition: all 0.2s;
    transition: all 0.2s;
    padding: 0.3rem 0.5rem; }
    .search-content__only-title:hover {
      background-color: #E8E8E8;
      cursor: pointer; }
    .search-content__only-title input[type=checkbox] {
      background-color: white; }
      .search-content__only-title input[type=checkbox]:hover {
        cursor: pointer; }
    .search-content__only-title label {
      margin-bottom: 0 !important; }
      .search-content__only-title label:hover {
        cursor: pointer; }

.search-filters {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background: #E8E8E8;
  -ms-flex-line-pack: center;
      align-content: center;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-direction: column;
      flex-direction: column;
  padding: 0.2rem 0; }
  @media only screen and (max-width: 1640px) {
    .search-filters {
      -ms-flex-wrap: wrap;
          flex-wrap: wrap; } }
  .search-filters__date-range {
    background-color: white;
    border: 1px solid #fff;
    border-radius: 0.375rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    position: relative;
    -webkit-transition: all 0.2s;
    transition: all 0.2s;
    margin-left: 0.75rem;
    height: 2rem; }
    .search-filters__date-range:hover {
      border: 1px solid #8A8A8A; }
    .search-filters__date-range input[type=date] {
      border: none;
      background-color: white;
      -webkit-transition: all 0.2s;
      transition: all 0.2s;
      position: relative;
      padding-top: 0;
      padding-bottom: 0; }
      .search-filters__date-range input[type=date]::-webkit-calendar-picker-indicator {
        opacity: 0;
        z-index: 10;
        cursor: pointer; }
      .search-filters__date-range input[type=date]:after {
        font-family: 'MaterialIcons';
        content: '\e916';
        position: relative;
        left: -1rem;
        font-size: 1.25rem;
        margin-bottom: 0.1rem; }
    .search-filters__date-range span {
      font-size: 2rem; }
  .search-filters__fields {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center; }
    .search-filters__fields-field {
      position: relative;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center;
      -webkit-box-pack: center;
          -ms-flex-pack: center;
              justify-content: center;
      border-radius: 0.5rem;
      -webkit-transition: all 0.2s;
      transition: all 0.2s;
      padding: 0.3rem 0.5rem; }
      .search-filters__fields-field-container {
        padding: 0.3rem 0.5rem;
        -webkit-transition: all 0.2s;
        transition: all 0.2s;
        border-radius: 0.5rem;
        -webkit-box-align: center;
            -ms-flex-align: center;
                align-items: center;
        -webkit-box-pack: center;
            -ms-flex-pack: center;
                justify-content: center;
        position: relative;
        font-size: 0.85rem; }
        .search-filters__fields-field-container:hover {
          background-color: #CACACA;
          cursor: pointer; }
        .search-filters__fields-field-container-selected {
          display: inline-block;
          min-width: 1.25rem;
          position: relative;
          left: 0.25rem; }
      .search-filters__fields-field .m-icon {
        font-size: 1.75rem; }
      .search-filters__fields-field-options {
        position: absolute;
        top: 2.5rem;
        left: -1.5rem;
        background-color: white;
        min-height: 4rem;
        min-width: 15rem;
        z-index: 50;
        border: 2px solid #E8E8E8;
        border-radius: 0.75rem;
        cursor: auto;
        display: block; }
        .search-filters__fields-field-options.active {
          display: block; }
        .search-filters__fields-field-options ul {
          min-width: 5rem;
          padding: 0.5rem;
          overflow: auto;
          max-height: 25rem; }
          .search-filters__fields-field-options ul li {
            padding: 0.2rem 0.2rem;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
                -ms-flex-align: center;
                    align-items: center;
            margin-bottom: 0.25rem; }
            .search-filters__fields-field-options ul li input[type=checkbox] {
              width: 1.05rem;
              height: 1.05rem;
              background-color: white;
              border: 1px solid #CACACA;
              position: relative;
              font-size: 0.7rem; }
              .search-filters__fields-field-options ul li input[type=checkbox]:hover {
                cursor: pointer; }
              .search-filters__fields-field-options ul li input[type=checkbox]:checked:before {
                position: absolute;
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-pack: center;
                    -ms-flex-pack: center;
                        justify-content: center;
                -webkit-box-align: center;
                    -ms-flex-align: center;
                        align-items: center;
                top: -0;
                left: 0;
                width: 1rem;
                height: 1rem;
                font-family: 'MaterialIconsOutlined';
                content: 'check';
                background: #0073BE;
                border-color: #0073BE;
                border-radius: 3px;
                color: #fff; }
            .search-filters__fields-field-options ul li label {
              font-size: 0.75rem;
              margin-bottom: 0;
              color: #8A8A8A;
              padding-top: 0; }
              .search-filters__fields-field-options ul li label:hover {
                cursor: pointer; }
  .search-filters-buttons {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center; }

/*Table*/
.filter-table-image {
  width: 1.5rem;
  height: 1.5rem; }

.tag-pill {
  font-size: .85em;
  display: inline-block;
  margin: 0 5px 5px 0;
  padding: 3px 7px;
  border-radius: 12px;
  color: #FFFFFF;
  background-color: #005C98; }

/* Newsletter resends */
#searchFailures a, button {
  margin-left: 10px; }

#searchFailures > div > div.flex.justify-end.flex-align-end {
  margin-bottom: 5px; }

.newsletter-article-page-container {
  max-height: 55rem; }

.newsletter-article-item {
  font-size: 0.85rem; }
  .newsletter-article-item .highlight-title {
    font-weight: bold;
    padding-top: 0.2rem; }
  .newsletter-article-item a {
    word-break: normal; }

.m-icon-medium {
  font-size: 2rem !important; }

.app-title {
  width: 150px; }

.login-box a {
  width: 100%; }

.login-box .app-title {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 0; }
  .login-box .app-title .dual-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    font-size: 2rem; }
    .login-box .app-title .dual-title span {
      text-align: left; }
    .login-box .app-title .dual-title .app-title-regular {
      font-family: "cencora-gilroy", Arial, Helvetica, Verdana, sans-serif; }

.app-title h1 {
  font-size: 21px;
  font-weight: 500;
  line-height: 1; }
  .app-title h1 span {
    display: block; }
    .app-title h1 span.app-title-bold {
      font-family: "cencora-gilroy-bold", Arial, Helvetica, Verdana, sans-serif; }

.app-title-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column; }

i.icon-zoom-out {
  display: block;
  width: 25px;
  height: 25px; }
  i.icon-zoom-out:before {
    font-family: MaterialIconsOutlined;
    content: "zoom_out";
    font-size: 1.5rem; }

.page-wrapper {
  padding: 1rem 1.875rem; }

.sub-header {
  margin-left: -1.875rem;
  margin-right: -1.875rem;
  margin-bottom: 1rem;
  margin-top: -1rem; }

.sub-header.sticky {
  top: 0;
  z-index: 9999; }

footer.sticky {
  bottom: 0; }

.buttons {
  width: auto;
  width: initial; }

.home-wrapper {
  margin-left: -1.875rem;
  margin-right: -1.875rem;
  margin-top: -1rem; }

select.inline {
  height: 41.6px; }

.custom-select.card:before {
  top: 22px;
  right: 22px; }

.nav-menu .m-icon {
  top: 3px; }

.account-navigation .nav-menu .m-icon {
  top: 1px; }

.custom-select:before {
  z-index: 1; }

.core-container > header.page-header {
  height: 50px; }

.nav-menu > li.full-menu .flyout {
  top: 50px; }

.terms {
  margin-top: 10px; }


/*# sourceMappingURL=smartDecisionsCss.css.map*/
input.button {
    font-family: "cencora-gilroy", Arial, Helvetica, Verdana, sans-serif;
    font-weight: 500;
    display: inline-block;
    padding: .5rem .75rem;
    border-radius: 5px;
    transition: all .3s;
    border: none;
    outline: none;
    width: fit-content;
    background: #0073be;
    color: #fff;
    font-size: .85rem;
    cursor: pointer;
    white-space: pre;
}