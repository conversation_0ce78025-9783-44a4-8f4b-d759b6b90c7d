﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>6237bc27-6162-4a95-a1b4-79770e12e732</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Azure.Search.Documents" Version="11.5.1" />
    <PackageReference Include="Microsoft.Graph" Version="5.77.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="PharmaLex.Authentication.B2C" Version="8.0.0.214" />
    <PackageReference Include="PharmaLex.AzureCloudStorage" Version="8.0.0.187" />
    <PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
    <PackageReference Include="Microsoft.CodeAnalysis.FxCopAnalyzers" Version="2.9.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.Text.Json" Version="9.0.4" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.SmartDecisions.Entities\PharmaLex.SmartDecisions.Entities.csproj" />
  </ItemGroup>

</Project>
