﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Axon.Core.Shared.Auth;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Authentication.B2C;
using PharmaLex.SmartDecisions.Web.Helpers;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    public partial class HomeController : BaseController
    {
        private readonly IAuthorizationService authorizationService;
        private readonly IConfiguration configuration;

        public HomeController(IAuthorizationService authorizationService, IConfiguration configuration)
        {
            this.authorizationService = authorizationService;
            this.configuration = configuration;
        }

        [HttpGet("/unauthorised"), AllowAnonymous]
        public IActionResult Unauthorised()
        {
            this.ViewData["unauthorised"] = true;
            ViewBag.ShowBackground = true;
            return View("Anonymous");
        }

        [HttpGet, AllowAnonymous]
        public async Task<IActionResult> Index([FromQuery]bool mp, [FromQuery]string postLoginRedirect = null)
        {
            if (!this.User.Identity.IsAuthenticated)
            {
                ViewData["landing"] = true;
                ViewData["PostLoginRedirect"] = string.IsNullOrEmpty(postLoginRedirect) ? "/" : postLoginRedirect;
                ViewBag.ShowBackground = true;
                return View("Anonymous");
            }

            var userEmail = this.User.Claims.FirstOrDefault(c => c.Type == AxonClaimTypes.Email)?.Value;
            var isPlxUser = this.User.IsPharmaLexUser() ||  EmailHelper.IsInternalDomainUser(userEmail);
            if (isPlxUser)
            {
                if (!mp && (await authorizationService.AuthorizeAsync(this.User, "NewsAuthor")).Succeeded)
                {
                    return Redirect("/articles");
                }
                else
                {
                    return this.View();
                }
            }
            else
            {
                bool isDecisionsUser = (await authorizationService.AuthorizeAsync(this.User, "DecisionsSubscriber")).Succeeded;
                if(isDecisionsUser)
                {
                    return this.View();
                }

                bool isNewsUser = (await authorizationService.AuthorizeAsync(this.User, "NewsSubscriber")).Succeeded;
                if (isNewsUser)
                {
                    return Redirect("/articles/search");
                }

                //should never get here as non subscriber users get redirected to unauthorized view at log on
                return this.Unauthorized();
            }
        }

        [Route("/login")]
        public IActionResult Login(string path)
        {
            path = (path ?? string.Empty).Trim().Trim('/').Trim();

            if (path.Contains("//") ||
                path.Contains(":\\") ||
                path.Contains(".") ||
                path.Contains(" "))
            {
                throw new System.Exception("Invalid URL path.");
            }

            if (Url.IsLocalUrl($"/{path}"))
            {
                return this.LocalRedirect($"/{path}");
            }
            else
            {
                throw new System.Exception("Sorry! It is not a local url.");
            }
        }

        [Route("/logout")]
        public IActionResult Logout()
        {
            return this.SignOut(new AuthenticationProperties { RedirectUri = "/" }, CookieAuthenticationDefaults.AuthenticationScheme, OpenIdConnectDefaults.AuthenticationScheme);
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return this.View();
        }

        [HttpPost, AllowAnonymous]
        public IActionResult SetLanguage(string culture, string returnUrl)
        {
            Response.Cookies.Append(
                CookieRequestCultureProvider.DefaultCookieName,
                CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture)),
                new CookieOptions { 
                    Expires = DateTimeOffset.UtcNow.AddYears(1),
                    Secure = true,
                    HttpOnly = true
                }
            );

            return LocalRedirect(returnUrl);
        }
    }
}
