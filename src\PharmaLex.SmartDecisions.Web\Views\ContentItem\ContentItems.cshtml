﻿@model ListContentItemViewModel
@using PharmaLex.SmartDecisions.Entities
@using PharmaLex.Caching.Data
@inject IDistributedCacheServiceFactory cache
@{
    ViewData["Title"] = "Index";
}

<div id="content-items">
    @Html.AntiForgeryToken()
    <div class="sub-header">
        <h2>@Model.ContentType.PluralName</h2>
        <div class="controls">
            <a class="button" href="/data/new/@Model.ContentType.Id">Add</a>
            <a class="button secondary" href="/data/export/@Model.ContentType.Id"> Export</a>
            <a class="button secondary" v-on:click.prevent="showImportDialog()"> Import</a>
        </div>
    </div>

    <filtered-table styling="add-table"
                    id="contentItemsTable"
                    :pager-location="'topbottom'"
                    :items="items"
                    :columns="columns"
                    :filters="filters"
                    :no-records-message="'@($"No \\'{Model.ContentType.Name}\\' records found")'"
                    :link="link">
    </filtered-table>

    <div id="import-items-dialog" :class="['modal-mask',importDialogClass]" v-cloak>
        <div class="dialog-container modal-wrapper">
            <div class="modal-container dialog-content">
                <div class="modal-header">
                    <h3>Import @Model.ContentType.PluralName</h3>
                    <i class="m-icon" v-on:click="resetImport">close</i>
                </div>
                <div id="drop-zone" :class="dropZoneClass"></div>
                <div id="import-progress" :class="importProgressClass">
                    <img src="/images/loaders/spinner-75.svg" alt="loader"/>
                </div>
                <div id="import-results" :class="importResultsClass" class="modal-body" style="max-height:70vh">
                    <div class="import-summary">
                        <i class="m-icon">info</i>
                        <p>{{uploadedItemCount}} items processed of which {{uploadedItemCount - importErrors.length}} were successfully imported.</p>
                    </div>
                            
                    <div vm-if="importErrors.length > 0" class="import-errors">
                        <h3 class="lead error-color bold">Errors:</h3>
                        <ul>
                            <li v-for="e in importErrors">{{e}}</li>
                        </ul>
                    </div>
                </div>
                <div class="buttons">
                    <a class="button" v-on:click="resetImport">Close</a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="@VersionCdn.GetUrl("lib/uppy/uppy.min.js")"></script>
    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        var pageConfig = {
            appElement: '#content-items',
            data: function () {
                return {
                    link: '/data/edit/',
                    items: @Html.Raw(Model.Items.Select(x =>
                      {
                          foreach (var pl in x.ContentType.Field.Where(y => y.Type == "picklist" || y.Type == "relationship"))
                          {
                              var fvm = x.Values.FirstOrDefault(y => y.FieldId == pl.Id);
                              if (fvm != null)
                              {
                                  var plc = cache.CreateMappedEntity<ContentItem, PicklistItemModel>();
                                  if (pl.MultiSelect)
                                  {
                                      fvm.Value = String.Join(", ", plc.Where(y => y.ContentTypeId == pl.RelatedContentTypeId && fvm.Value.Split("|", StringSplitOptions.None).Select(z => Int32.Parse(z)).Contains(y.Id)).Select(y => y.Name));
                                  }
                                  else
                                  {
                                      fvm.Value = plc.FirstOrDefault(y => y.ContentTypeId == pl.RelatedContentTypeId && y.Id == Int32.Parse(fvm.Value))?.Name;
                                  }
                              }
                          }
                          return x;
                      }).ToDynamicJson()),
                    filters: @Html.Raw(Model.Filters.ToJson()),
                    columns: {
                        idKey: 'Id',
                        config: @Html.Raw(Model.Columns.ToJson())
                    },
                    showImport: false,
                    importing: false,
                    imported: false,
                    uploadedItemCount: 0,
                    importErrors: []
                };
            },
            methods: {
                showImportDialog: function () {
                    this.showImport = true;
                },
                resetImport: function () {
                    document.location.reload();
                }
            },
            computed: {
                importDialogClass: function () {
                    return this.showImport ? 'dialog-surface' : 'hidden';
                },
                importProgressClass: function () {
                    return this.importing ? 'import-progress' : 'hidden';
                },
                dropZoneClass: function () {
                    return this.importing || this.imported ? 'hidden' : 'import-items-drop-zone';
                },
                importResultsClass: function () {
                    return this.imported ? 'import-results' : 'hidden';
                }
            },
            mounted() {
                let uppy = Uppy.Core({
                    debug: true,
                    autoProceed: true,
                    restrictions: {
                        maxNumberOfFiles: 1
                    }
                });
                uppy.use(Uppy.DragDrop, {
                    target: '#drop-zone',
                    height: '400px',
                    note: ' - single Excel(.xslx) file with @Model.ContentType.PluralName records'
                })
                .use(Uppy.XHRUpload, {
                    endpoint: `/data/import/@Model.ContentType.Id`,
                    headers: {
                        'RequestVerificationToken': token,
                    },
                    formData: true,
                    timeout: 0,
                    fieldName: 'file'
                });

                uppy.on('upload', (data) => {
                    this.importing = true;
                });

                uppy.on('upload-success', (file, r, uploadURL) => {
                    this.uploadedItemCount = r.count;
                    this.importErrors = r.errors;
                    this.importing = false;
                    this.imported = true;
                });

                uppy.on('upload-error', (file, error, response) => {
                    document.querySelector('.dialog-container').classList.add('hidden');
                    plx.toast.show('An error occured importing @Model.ContentType.PluralName, please try again.', 2, 'failed', null, 2500, { useIcons: true });
                    setTimeout(() => document.location.reload(), 2000);
                });

                document.querySelectorAll('#import-items-dialog, .dialog-closer').forEach(x => {
                    x.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.showImport = false;
                    });
                });
                document.querySelector('.dialog-container').addEventListener('click', (e) => e.stopPropagation());
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTableV2" />
}
