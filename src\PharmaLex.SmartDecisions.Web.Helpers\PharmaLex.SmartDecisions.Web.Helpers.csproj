﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	<Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="AutoMapper" Version="13.0.1" />
	<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.0" />
    <PackageReference Include="Azure.Search.Documents" Version="11.5.1" />
	<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Azure.Security.KeyVault.Keys" Version="4.5.0" />
    <PackageReference Include="PharmaLex.Caching" Version="*********" />
    <PackageReference Include="PharmaLex.Caching.Data" Version="*********" />
    <PackageReference Include="PharmaLex.Helpers" Version="*********" />
    <PackageReference Include="PharmaLex.Office" Version="*********" />
    <PackageReference Include="PharmaLex.HtmlToPdfConverter" Version="*********" />
    <PackageReference Include="System.Drawing.Common" Version="6.0.0" />
    <PackageReference Include="SharpZipLib" Version="1.4.2" />
    <PackageReference Include="System.Text.Json" Version="9.0.4" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageReference Include="SendGrid" Version="9.28.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.SmartDecisions.Entities\PharmaLex.SmartDecisions.Entities.csproj" />
    <ProjectReference Include="..\PharmaLex.SmartDecisions.Web.Models\PharmaLex.SmartDecisions.Web.Models.csproj" />
  </ItemGroup>

</Project>
